import React, { useState, useMemo, useCallback } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Button } from 'primereact/button';
import { DateTime } from 'luxon';
import moment from 'moment';
import { Calendar } from 'primereact/calendar';
import Swal from 'sweetalert2';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { TabView, TabPanel } from 'primereact/tabview';
import ActionDetailsDialog from './components/ActionDetailsDialog';

const categoryOptions = [
    { label: 'Good Practices', value: 1 },
    { label: 'Opportunity for Improvement', value: 2 },
    { label: 'Non-compliance', value: 3 },
];

const nonComplianceOptions = [
    { label: 'Regulatory (Major)', value: 1 },
    { label: 'Regulatory (Minor)', value: 2 },
    { label: 'Minor', value: 3 },
];

const SupplierActionPendingTable = ({ data, assessorList }) => {
    const [globalFilter, setGlobalFilter] = useState('');
    const [selectedAction, setSelectedAction] = useState(null);
    const [isDialogVisible, setIsDialogVisible] = useState(false);
    const [activeNestedTabIndex, setActiveNestedTabIndex] = useState(0);
    const [dateFilter, setDateFilter] = useState({ start: null, end: null });

    // Add filter state management for each DataTable
    const [allActionsFilters, setAllActionsFilters] = useState({
        vendorName: { matchMode: 'in', value: null },
        finding: { matchMode: 'in', value: null },
        description: { matchMode: 'in', value: null },
        categoryOfFinding: { matchMode: 'in', value: null },
        nonComplianceType: { matchMode: 'in', value: null },
        status: { matchMode: 'in', value: null },
        created_by: { matchMode: 'in', value: null }
    });

    const [goodPracticesFilters, setGoodPracticesFilters] = useState({
        vendorName: { matchMode: 'in', value: null },
        finding: { matchMode: 'in', value: null },
        description: { matchMode: 'in', value: null },
        status: { matchMode: 'in', value: null },
        created_by: { matchMode: 'in', value: null }
    });

    const [opportunityFilters, setOpportunityFilters] = useState({
        vendorName: { matchMode: 'in', value: null },
        finding: { matchMode: 'in', value: null },
        description: { matchMode: 'in', value: null },
        status: { matchMode: 'in', value: null },
        created_by: { matchMode: 'in', value: null }
    });

    const [nonComplianceFilters, setNonComplianceFilters] = useState({
        vendorName: { matchMode: 'in', value: null },
        finding: { matchMode: 'in', value: null },
        description: { matchMode: 'in', value: null },
        nonComplianceType: { matchMode: 'in', value: null },
        status: { matchMode: 'in', value: null },
        created_by: { matchMode: 'in', value: null }
    });

    // Get all supplier actions with additional properties - filter only type 1 and type 2
    const allSupplierActions = useMemo(() => {
        return data?.flatMap(item =>
            item?.supplierActions
                ?.filter(action => action.type === 1 || action.type === 2)
                ?.map(action => ({
                    ...action,
                    vendorName: item?.vendor?.supplierName,
                    vendorLocation: item?.location,
                    msiId: item?.msiId,
                    stat: item?.stat,
                    team1Members: item?.team1Members,
                    team2Members: item?.team2Members,
                    team3Members: item?.team3Members,
                    team4Members: item?.team4Members,
                    auditStartDate: item?.auditStartDate,
                    auditEndDate: item?.auditEndDate,
                    assessmentStartDate: item?.assessmentStartDate,
                    assessmentEndDate: item?.assessmentEndDate
                })) || []
        ) || [];
    }, [data]);

    // Apply date filter to actions
    const supplierActions = useMemo(() => {
        let filteredActions = [...allSupplierActions];

        // Apply date range filter if both start and end dates are set
        if (dateFilter.start && dateFilter.end) {
            filteredActions = filteredActions.filter(action => {
                
                if (!action.created_on) return true;

                const actionDate = DateTime.fromISO(action.created_on).toJSDate();
                const startDate = moment(dateFilter.start).startOf('day').toDate();
                const endDate = moment(dateFilter.end).endOf('day').toDate();

                return actionDate >= startDate && actionDate <= endDate;
            });
        }

        return filteredActions;
    }, [allSupplierActions, dateFilter]);

    // Filter actions by category
    const goodPracticesActions = useMemo(() => {
        return supplierActions.filter(action => action.categoryOfFinding === 1);
    }, [supplierActions]);

    const opportunityOfImprovementActions = useMemo(() => {
        return supplierActions.filter(action => action.categoryOfFinding === 2);
    }, [supplierActions]);

    const nonComplianceActions = useMemo(() => {
        return supplierActions.filter(action => action.categoryOfFinding === 3);
    }, [supplierActions]);

    const getUserName = (id) => {
        const user = assessorList?.find(u => u.id === id);
        return user?.information?.empname || `User ID: ${id}`;
    };

    const getCategoryLabel = (id) => categoryOptions.find(option => option.value === id)?.label || `Category ID: ${id}`;
    const getNonComplianceLabel = (id) => id ? nonComplianceOptions.find(option => option.value === id)?.label || `Type ID: ${id}` : 'NA';
    const getStatusLabel = (status) => status ? status : 'Assigned';

    const dateTemplate = (rowData, field) => {
        const dateStr = rowData?.[field];
        return dateStr ? DateTime.fromISO(dateStr).toFormat('dd-MM-yyyy') : 'NA';
    };

    const getUniqueOptions = (field, labelMapFn) => {
        let values = [...new Set(supplierActions.map(d => d[field]))];
        if (field === 'status' && !values.includes(null)) values.push(null);

        return values.map(val => ({
            label: val ? (labelMapFn ? labelMapFn(val) : val) : 'Assigned',
            value: val
        }));
    };

    const createMultiSelectFilter = (field, options) => {
        return (optionsProps) => (
            <MultiSelect
                value={optionsProps.value}
                options={options}
                optionLabel="label"
                onChange={(e) => optionsProps.filterCallback(e.value)}
                placeholder={`Select ${field}`}
                filter
                panelClassName="hidefilter"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };

    // Use useCallback to prevent unnecessary re-renders when opening dialogs
    const showDialog = useCallback((action) => {
        setSelectedAction(action);
        setIsDialogVisible(true);
    }, []);

    const hideDialog = useCallback(() => {
        setSelectedAction(null);
        setIsDialogVisible(false);
    }, []);


    // Function to render Action ID as a clickable link
    const actionIdTemplate = (rowData) => {
        return (
            <button
                onClick={() => showDialog(rowData)}
                className="text-primary p-link"
                style={{ textDecoration: 'underline', cursor: 'pointer', background: 'none', border: 'none', padding: 0 }}
            >
                {rowData.msiId} {rowData.actionId}
            </button>
        );
    };

    const exportExcel = () => {
        if (!supplierActions || supplierActions.length === 0) {
            Swal.fire('Warning', 'No actions to export.', 'warning');
            return;
        }

        // Get the currently displayed actions based on the active tab
        let actionsToExport = supplierActions;
        if (activeNestedTabIndex === 1) {
            actionsToExport = goodPracticesActions;
        } else if (activeNestedTabIndex === 2) {
            actionsToExport = opportunityOfImprovementActions;
        } else if (activeNestedTabIndex === 3) {
            actionsToExport = nonComplianceActions;
        }

        // Create export data with date filter information
        const exportData = actionsToExport.map((action, index) => ({
            'S.No': index + 1,
            'Supplier Name': action.vendorName || 'NA',
            'MSI ID': action.msiId || 'NA',
            'Finding': action.finding || 'NA',
            'Description': action.description || 'NA',
            'Category': getCategoryLabel(action.categoryOfFinding) || 'NA',
            'Non-Compliance Type': getNonComplianceLabel(action.nonComplianceType) || 'NA',
            'Status': getStatusLabel(action.status),
            'Due Date': action.actionDueDate ? DateTime.fromISO(action.actionDueDate).toFormat('dd-MM-yyyy') : 'NA',
            'Created On': action.created_on ? DateTime.fromISO(action.created_on).toFormat('dd-MM-yyyy') : 'NA',
            'Created By': getUserName(action.created_by)
        }));

        // Add date filter information to the filename
        let filename = 'Pending_Actions';
        if (dateFilter.start && dateFilter.end) {
            const startStr = moment(dateFilter.start).format('YYYYMMDD');
            const endStr = moment(dateFilter.end).format('YYYYMMDD');
            filename += `_${startStr}_to_${endStr}`;
        }
        filename += `_${moment().format('YYYYMMDD_HHmmss')}.xlsx`;

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'PendingActions');

        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, filename);
    };

    // Function to clear date filter
    const clearDateFilter = () => {
        setDateFilter({ start: null, end: null });
    };

    return (
        <div className="card">
            <div className="flex justify-content-between align-items-center mb-3">
                <div className="flex align-items-center">
                    <h3 className="m-0 mr-3 me-3">Pending Actions</h3>
                    {/* <Button
                        label="Add Action"
                        icon="pi pi-plus"
                        className="p-button-success mr-2"
                        onClick={showAddDialog}
                    /> */}
                    <Button
                        label="Export Excel"
                        icon="pi pi-file-excel"
                        className="p-button-success p-button-outlined"
                        onClick={exportExcel}
                    />
                </div>

                <span className="p-input-icon-left">
                    <i className="pi pi-search" />
                    <InputText
                        value={globalFilter}
                        onChange={(e) => setGlobalFilter(e.target.value)}
                        placeholder="Global Search"
                    />
                </span>
            </div>

            {/* Date filter layout from completed tab */}
            <div className="col-12 flex justify-content-between align-items-end mb-3 gap-4">
                {/* Date Range Filter - Left */}
                <div className="flex gap-3 align-items-center">
                    <div className="flex flex-column">
                        <label className="mb-1">Date From</label>
                        <Calendar
                            value={dateFilter.start}
                            onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                            placeholder="Start Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                        />
                    </div>
                    <div className="flex flex-column">
                        <label className="mb-1">To</label>
                        <Calendar
                            value={dateFilter.end}
                            onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                            placeholder="End Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                            minDate={dateFilter.start}
                            disabled={!dateFilter.start}
                        />
                    </div>
                    {(dateFilter.start || dateFilter.end) && (
                        <button
                            className="btn btn-sm btn-outline-secondary align-self-end mb-1"
                            onClick={clearDateFilter}
                            style={{ height: '36px' }}
                        >
                            Clear
                        </button>
                    )}
                </div>
            </div>

            <TabView
                activeIndex={activeNestedTabIndex}
                onTabChange={(e) => setActiveNestedTabIndex(e.index)}
            >
                <TabPanel header="All Actions">
                    <DataTable
                        value={supplierActions}
                        paginator
                        rows={10}
                         rowsPerPageOptions={[10, 25, 50, 100,150,200,300,500]}
                        globalFilter={globalFilter}
                        filterDisplay="menu"
                        scrollable
                        scrollHeight="500px"
                        className="p-datatable-gridlines"
                        emptyMessage="No supplier actions found."
                        removableSort
                        filters={allActionsFilters}
                        onFilter={(e) => {
                            // Create a copy of the filters object
                            const cleanedFilters = { ...e.filters };

                            // Remove the null key if it exists
                            if (cleanedFilters.hasOwnProperty('null')) {
                                delete cleanedFilters['null'];
                            }

                            // Update our filter state
                            setAllActionsFilters(cleanedFilters);
                        }}
                    >
                        <Column field="id" header="Action ID" body={actionIdTemplate} sortable />
                        <Column
                            field="vendorName"
                            header="Supplier Name"
                            sortable
                            filter
                            filterField="vendorName"
                            filterElement={createMultiSelectFilter('Supplier', getUniqueOptions('vendorName'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="finding"
                            header="Finding"
                            sortable
                            filter
                            filterField="finding"
                            filterElement={createMultiSelectFilter('Finding', getUniqueOptions('finding'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="description"
                            header="Description"
                            sortable
                            filter
                            style={{ minWidth: '300px' }}
                            filterField="description"
                            filterElement={createMultiSelectFilter('Description', getUniqueOptions('description'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="categoryOfFinding"
                            header="Category"
                            sortable
                            filter
                            filterField="categoryOfFinding"
                            body={(rowData) => getCategoryLabel(rowData.categoryOfFinding)}
                            filterElement={createMultiSelectFilter('Category', categoryOptions)}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="nonComplianceType"
                            header="Non-Compliance Type"
                            sortable
                            filter
                            filterField="nonComplianceType"
                            body={(rowData) => getNonComplianceLabel(rowData.nonComplianceType)}
                            filterElement={createMultiSelectFilter('Non-Compliance Type', nonComplianceOptions)}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="status"
                            header="Status"
                            sortable
                            filter
                            filterField="status"
                            body={(rowData) => getStatusLabel(rowData.status)}
                            filterElement={createMultiSelectFilter('Status', getUniqueOptions('status'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="actionDueDate"
                            header="Due Date"
                            sortable
                            body={(rowData) => dateTemplate(rowData, 'actionDueDate')}
                        />
                        <Column
                            field="created_on"
                            header="Created On"
                            sortable
                            body={(rowData) => dateTemplate(rowData, 'created_on')}
                        />
                        <Column
                            field="created_by"
                            header="Created By"
                            sortable
                            filter
                            filterField="created_by"
                            body={(rowData) => getUserName(rowData.created_by)}
                            filterElement={createMultiSelectFilter('Created By', getUniqueOptions('created_by', getUserName))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />

                    </DataTable>
                </TabPanel>

                <TabPanel header={`Good Practices (${goodPracticesActions.length})`}>
                    <DataTable
                        value={goodPracticesActions}
                        paginator
                        rows={10}
                         rowsPerPageOptions={[10, 25, 50, 100,150,200,300,500]}
                        globalFilter={globalFilter}
                        filterDisplay="menu"
                        scrollable
                        scrollHeight="500px"
                        className="p-datatable-gridlines"
                        emptyMessage="No Good Practices actions found."
                        removableSort
                        filters={goodPracticesFilters}
                        onFilter={(e) => {
                            // Create a copy of the filters object
                            const cleanedFilters = { ...e.filters };

                            // Remove the null key if it exists
                            if (cleanedFilters.hasOwnProperty('null')) {
                                delete cleanedFilters['null'];
                            }

                            // Update our filter state
                            setGoodPracticesFilters(cleanedFilters);
                        }}
                    >
                        <Column field="id" header="Action ID" body={actionIdTemplate} sortable />
                        <Column
                            field="vendorName"
                            header="Supplier Name"
                            sortable
                            filter
                            filterField="vendorName"
                            filterElement={createMultiSelectFilter('Supplier', getUniqueOptions('vendorName'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="finding"
                            header="Finding"
                            sortable
                            filter
                            filterField="finding"
                            filterElement={createMultiSelectFilter('Finding', getUniqueOptions('finding'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="description"
                            header="Description"
                            sortable
                            filter
                            style={{ minWidth: '300px' }}
                            filterField="description"
                            filterElement={createMultiSelectFilter('Description', getUniqueOptions('description'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="status"
                            header="Status"
                            sortable
                            filter
                            filterField="status"
                            body={(rowData) => getStatusLabel(rowData.status)}
                            filterElement={createMultiSelectFilter('Status', getUniqueOptions('status'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="actionDueDate"
                            header="Due Date"
                            sortable
                            body={(rowData) => dateTemplate(rowData, 'actionDueDate')}
                        />
                        <Column
                            field="created_on"
                            header="Created On"
                            sortable
                            body={(rowData) => dateTemplate(rowData, 'created_on')}
                        />
                        <Column
                            field="created_by"
                            header="Created By"
                            sortable
                            filter
                            filterField="created_by"
                            body={(rowData) => getUserName(rowData.created_by)}
                            filterElement={createMultiSelectFilter('Created By', getUniqueOptions('created_by', getUserName))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />

                    </DataTable>
                </TabPanel>

                <TabPanel header={`Opportunity for Improvement (${opportunityOfImprovementActions.length})`}>
                    <DataTable
                        value={opportunityOfImprovementActions}
                        paginator
                        rows={10}
                         rowsPerPageOptions={[10, 25, 50, 100,150,200,300,500]}
                        globalFilter={globalFilter}
                        filterDisplay="menu"
                        scrollable
                        scrollHeight="500px"
                        className="p-datatable-gridlines"
                        emptyMessage="No Opportunity for Improvement actions found."
                        removableSort
                        filters={opportunityFilters}
                        onFilter={(e) => {
                            // Create a copy of the filters object
                            const cleanedFilters = { ...e.filters };

                            // Remove the null key if it exists
                            if (cleanedFilters.hasOwnProperty('null')) {
                                delete cleanedFilters['null'];
                            }

                            // Update our filter state
                            setOpportunityFilters(cleanedFilters);
                        }}
                    >
                        <Column field="id" header="Action ID" body={actionIdTemplate} sortable />
                        <Column
                            field="vendorName"
                            header="Supplier Name"
                            sortable
                            filter
                            filterField="vendorName"
                            filterElement={createMultiSelectFilter('Supplier', getUniqueOptions('vendorName'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="finding"
                            header="Finding"
                            sortable
                            filter
                            filterField="finding"
                            filterElement={createMultiSelectFilter('Finding', getUniqueOptions('finding'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="description"
                            header="Description"
                            sortable
                            filter
                            style={{ minWidth: '300px' }}
                            filterField="description"
                            filterElement={createMultiSelectFilter('Description', getUniqueOptions('description'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="status"
                            header="Status"
                            sortable
                            filter
                            filterField="status"
                            body={(rowData) => getStatusLabel(rowData.status)}
                            filterElement={createMultiSelectFilter('Status', getUniqueOptions('status'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="actionDueDate"
                            header="Due Date"
                            sortable
                            body={(rowData) => dateTemplate(rowData, 'actionDueDate')}
                        />
                        <Column
                            field="created_on"
                            header="Created On"
                            sortable
                            body={(rowData) => dateTemplate(rowData, 'created_on')}
                        />
                        <Column
                            field="created_by"
                            header="Created By"
                            sortable
                            filter
                            filterField="created_by"
                            body={(rowData) => getUserName(rowData.created_by)}
                            filterElement={createMultiSelectFilter('Created By', getUniqueOptions('created_by', getUserName))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />

                    </DataTable>
                </TabPanel>

                <TabPanel header={`Non-compliance (${nonComplianceActions.length})`}>
                    <DataTable
                        value={nonComplianceActions}
                        paginator
                        rows={10}
                         rowsPerPageOptions={[10, 25, 50, 100,150,200,300,500]}
                        globalFilter={globalFilter}
                        filterDisplay="menu"
                        scrollable
                        scrollHeight="500px"
                        className="p-datatable-gridlines"
                        emptyMessage="No Non-compliance actions found."
                        removableSort
                        filters={nonComplianceFilters}
                        onFilter={(e) => {
                            // Create a copy of the filters object
                            const cleanedFilters = { ...e.filters };

                            // Remove the null key if it exists
                            if (cleanedFilters.hasOwnProperty('null')) {
                                delete cleanedFilters['null'];
                            }

                            // Update our filter state
                            setNonComplianceFilters(cleanedFilters);
                        }}
                    >
                         <Column field="id" header="Action ID" body={actionIdTemplate} sortable />
                        <Column
                            field="vendorName"
                            header="Supplier Name"
                            sortable
                            filter
                            filterField="vendorName"
                            filterElement={createMultiSelectFilter('Supplier', getUniqueOptions('vendorName'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="finding"
                            header="Finding"
                            sortable
                            filter
                            filterField="finding"
                            filterElement={createMultiSelectFilter('Finding', getUniqueOptions('finding'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="description"
                            header="Description"
                            sortable
                            filter
                            style={{ minWidth: '300px' }}
                            filterField="description"
                            filterElement={createMultiSelectFilter('Description', getUniqueOptions('description'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="nonComplianceType"
                            header="Non-Compliance Type"
                            sortable
                            filter
                            filterField="nonComplianceType"
                            body={(rowData) => getNonComplianceLabel(rowData.nonComplianceType)}
                            filterElement={createMultiSelectFilter('Non-Compliance Type', nonComplianceOptions)}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="status"
                            header="Status"
                            sortable
                            filter
                            filterField="status"
                            body={(rowData) => getStatusLabel(rowData.status)}
                            filterElement={createMultiSelectFilter('Status', getUniqueOptions('status'))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />
                        <Column
                            field="actionDueDate"
                            header="Due Date"
                            sortable
                            body={(rowData) => dateTemplate(rowData, 'actionDueDate')}
                        />
                        <Column
                            field="created_on"
                            header="Created On"
                            sortable
                            body={(rowData) => dateTemplate(rowData, 'created_on')}
                        />
                        <Column
                            field="created_by"
                            header="Created By"
                            sortable
                            filter
                            filterField="created_by"
                            body={(rowData) => getUserName(rowData.created_by)}
                            filterElement={createMultiSelectFilter('Created By', getUniqueOptions('created_by', getUserName))}
                            filterFunction={(value, filter) => !filter || filter.includes(value)}
                            showFilterMatchModes={false}
                        />

                    </DataTable>
                </TabPanel>
            </TabView>

            {/* Action Details Dialog */}
            <ActionDetailsDialog
                visible={isDialogVisible}
                onHide={hideDialog}
                action={selectedAction}
                getCategoryLabel={getCategoryLabel}
                getNonComplianceLabel={getNonComplianceLabel}
                getStatusLabel={getStatusLabel}
                getUserName={getUserName}
            />


        </div>
    );
};

// Custom CSS for the Action Details Dialog
const actionDetailsDialogStyles = `
    .action-details-dialog .p-dialog-content {
        padding: 1.5rem;
    }

    .action-details-dialog .p-card {
        height: 100%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .action-details-dialog .p-card .p-card-title {
        font-size: 1.2rem;
        color: #315975;
        margin-bottom: 1rem;
    }

    .action-details-dialog .p-card .p-card-content {
        padding: 0;
    }

    .action-details-dialog .field {
        margin-bottom: 1rem;
    }

    .action-details-dialog .field label {
        color: #6c757d;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
        display: block;
    }

    .action-details-dialog .p-tag {
        font-weight: 600;
    }

    .action-details-dialog .p-divider {
        margin: 0.5rem 0 1rem 0;
    }

    .action-details-dialog .text-lg {
        font-size: 1.1rem;
    }

    .action-details-dialog .font-semibold {
        font-weight: 600;
    }

    .action-details-dialog .border-round {
        border-radius: 4px;
    }

    .action-details-dialog .bg-gray-50 {
        background-color: #f8f9fa;
    }

    .action-details-dialog .border-1 {
        border: 1px solid #dee2e6;
    }

    .action-details-dialog .border-gray-300 {
        border-color: #dee2e6;
    }

    .action-details-dialog .p-2 {
        padding: 0.5rem;
    }
`;

// Add the styles to the document
if (typeof document !== 'undefined') {
    const style = document.createElement('style');
    style.appendChild(document.createTextNode(actionDetailsDialogStyles));
    document.head.appendChild(style);
}

export default SupplierActionPendingTable;
